---
triple:          'x86_64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/todo_list_app.app/Contents/MacOS/todo_list_app'
relocations:
  - { offset: 0xD0F76, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowC12awakeFromNibyyFTo', symObjAddr: 0x0, symBinAddr: 0x100001500, symSize: 0x100 }
  - { offset: 0xD10AF, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowCMa', symObjAddr: 0x1B0, symBinAddr: 0x1000016B0, symSize: 0x20 }
  - { offset: 0xD10C3, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app24RegisterGeneratedPlugins8registryySo21FlutterPluginRegistry_p_tFTf4e_nSo0H14ViewControllerC_Tg5', symObjAddr: 0x1D0, symBinAddr: 0x1000016D0, symSize: 0x1B7 }
  - { offset: 0xD11C6, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowC12awakeFromNibyyFTo', symObjAddr: 0x0, symBinAddr: 0x100001500, symSize: 0x100 }
  - { offset: 0xD1301, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowC11contentRect9styleMask7backing5deferACSo6CGRectV_So013NSWindowStyleJ0VSo18NSBackingStoreTypeVSbtcfcTo', symObjAddr: 0x100, symBinAddr: 0x100001600, symSize: 0x80 }
  - { offset: 0xD135B, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowCfD', symObjAddr: 0x180, symBinAddr: 0x100001680, symSize: 0x30 }
  - { offset: 0xD1502, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateC47applicationShouldTerminateAfterLastWindowClosedySbSo13NSApplicationCFTo', symObjAddr: 0x0, symBinAddr: 0x100001890, symSize: 0x10 }
  - { offset: 0xD15C8, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateCMa', symObjAddr: 0xB0, symBinAddr: 0x100001940, symSize: 0x14 }
  - { offset: 0xD1616, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateC47applicationShouldTerminateAfterLastWindowClosedySbSo13NSApplicationCFTo', symObjAddr: 0x0, symBinAddr: 0x100001890, symSize: 0x10 }
  - { offset: 0xD162A, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateC40applicationSupportsSecureRestorableStateySbSo13NSApplicationCFTo', symObjAddr: 0x10, symBinAddr: 0x1000018A0, symSize: 0x10 }
  - { offset: 0xD1657, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateCACycfcTo', symObjAddr: 0x20, symBinAddr: 0x1000018B0, symSize: 0x30 }
  - { offset: 0xD1693, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateCfD', symObjAddr: 0x50, symBinAddr: 0x1000018E0, symSize: 0x30 }
  - { offset: 0xD16FD, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x80, symBinAddr: 0x100001910, symSize: 0x30 }
  - { offset: 0xD18A3, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app19ResourceBundleClass33_58A39A8AFD3E99F057B77AEB23A0ACFCLLCfD', symObjAddr: 0x0, symBinAddr: 0x100001960, symSize: 0x20 }
  - { offset: 0xD18E5, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app19ResourceBundleClass33_58A39A8AFD3E99F057B77AEB23A0ACFCLLCMa', symObjAddr: 0x20, symBinAddr: 0x100001980, symSize: 0x14 }
  - { offset: 0xD1901, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app19ResourceBundleClass33_58A39A8AFD3E99F057B77AEB23A0ACFCLLCfD', symObjAddr: 0x0, symBinAddr: 0x100001960, symSize: 0x20 }
...
