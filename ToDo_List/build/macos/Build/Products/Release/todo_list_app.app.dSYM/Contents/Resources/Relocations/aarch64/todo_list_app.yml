---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/todo_list_app.app/Contents/MacOS/todo_list_app'
relocations:
  - { offset: 0xCE02F, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowC12awakeFromNibyyFTo', symObjAddr: 0x0, symBinAddr: 0x100001520, symSize: 0xFC }
  - { offset: 0xCE168, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowCMa', symObjAddr: 0x1B8, symBinAddr: 0x1000016D8, symSize: 0x20 }
  - { offset: 0xCE17C, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app24RegisterGeneratedPlugins8registryySo21FlutterPluginRegistry_p_tFTf4e_nSo0H14ViewControllerC_Tg5', symObjAddr: 0x1D8, symBinAddr: 0x1000016F8, symSize: 0x1CC }
  - { offset: 0xCE26F, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowC12awakeFromNibyyFTo', symObjAddr: 0x0, symBinAddr: 0x100001520, symSize: 0xFC }
  - { offset: 0xCE3C0, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowC11contentRect9styleMask7backing5deferACSo6CGRectV_So013NSWindowStyleJ0VSo18NSBackingStoreTypeVSbtcfcTo', symObjAddr: 0xFC, symBinAddr: 0x10000161C, symSize: 0x8C }
  - { offset: 0xCE40D, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app17MainFlutterWindowCfD', symObjAddr: 0x188, symBinAddr: 0x1000016A8, symSize: 0x30 }
  - { offset: 0xCE5B4, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateC47applicationShouldTerminateAfterLastWindowClosedySbSo13NSApplicationCFTo', symObjAddr: 0x0, symBinAddr: 0x1000018C4, symSize: 0x8 }
  - { offset: 0xCE67A, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateCMa', symObjAddr: 0xA4, symBinAddr: 0x100001968, symSize: 0x20 }
  - { offset: 0xCE6C8, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateC47applicationShouldTerminateAfterLastWindowClosedySbSo13NSApplicationCFTo', symObjAddr: 0x0, symBinAddr: 0x1000018C4, symSize: 0x8 }
  - { offset: 0xCE6DC, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateC40applicationSupportsSecureRestorableStateySbSo13NSApplicationCFTo', symObjAddr: 0x8, symBinAddr: 0x1000018CC, symSize: 0x8 }
  - { offset: 0xCE709, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateCACycfcTo', symObjAddr: 0x10, symBinAddr: 0x1000018D4, symSize: 0x3C }
  - { offset: 0xCE745, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app11AppDelegateCfD', symObjAddr: 0x4C, symBinAddr: 0x100001910, symSize: 0x30 }
  - { offset: 0xCE7AF, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x7C, symBinAddr: 0x100001940, symSize: 0x28 }
  - { offset: 0xCE955, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app19ResourceBundleClass33_58A39A8AFD3E99F057B77AEB23A0ACFCLLCfD', symObjAddr: 0x0, symBinAddr: 0x100001988, symSize: 0x10 }
  - { offset: 0xCE997, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app19ResourceBundleClass33_58A39A8AFD3E99F057B77AEB23A0ACFCLLCMa', symObjAddr: 0x10, symBinAddr: 0x100001998, symSize: 0x20 }
  - { offset: 0xCE9B3, size: 0x8, addend: 0x0, symName: '_$s13todo_list_app19ResourceBundleClass33_58A39A8AFD3E99F057B77AEB23A0ACFCLLCfD', symObjAddr: 0x0, symBinAddr: 0x100001988, symSize: 0x10 }
...
