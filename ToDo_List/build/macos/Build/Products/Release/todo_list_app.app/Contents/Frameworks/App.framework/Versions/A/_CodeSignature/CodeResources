<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		7zfVkem4L9eyF/Y1+BqaGLsXgNk=
		</data>
		<key>Resources/flutter_assets/AssetManifest.bin</key>
		<data>
		43B7FJa+tvpnt2I0Jn2gnbLgR94=
		</data>
		<key>Resources/flutter_assets/AssetManifest.json</key>
		<data>
		HWGTgixw3hRe/Td28V/51yREZlA=
		</data>
		<key>Resources/flutter_assets/FontManifest.json</key>
		<data>
		HuqT/dgvrTHOMum5Qo5BXfxzfaM=
		</data>
		<key>Resources/flutter_assets/NOTICES.Z</key>
		<data>
		wtV//QVLn2h65pXNjtS76dPQpMY=
		</data>
		<key>Resources/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Resources/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		crHUQjByGC+4YQkIdNBTmUuzYLs=
		</data>
		<key>Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Vbslq1Oy/SBx7ergEVp5XGYvTLs=
		</data>
		<key>Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf</key>
		<data>
		7teGwHCZ+RaeOlmcHre5BoeqKQI=
		</data>
		<key>Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf</key>
		<data>
		UEcFTxvzqJS2KmMG9bunf3MuaM4=
		</data>
		<key>Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf</key>
		<data>
		TNvjYrjwA9L1LQxCEhvermwMybc=
		</data>
		<key>Resources/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CLA7WOT/JcZvXmAZOycoq1JGVjvE8lMhGlxINbvB0zM=
			</data>
		</dict>
		<key>Resources/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			iLs3cSVBJzgbBduM7MSWmSMA8f67QSB8W1RXIG/hWwY=
			</data>
		</dict>
		<key>Resources/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Nf4MKSrvgh69eN7LpdiHt8ClTnRT++QEdKMm4vQOmT0=
			</data>
		</dict>
		<key>Resources/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			LNlBG1QOXG4VrGVSOjYBvuZorsqRBOHeE2/DSzqRJ3E=
			</data>
		</dict>
		<key>Resources/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			ULh7CiLFKk8oM74CkMuVYxP1fsubH9P+5b/FMrifnF8=
			</data>
		</dict>
		<key>Resources/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Resources/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			WEHb2LU9jIluJIkNA6Cv2IeAKXB87Efa9jOPklvAN2s=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			JRiK3T2JRvArx/5GC24sClDrMfOMVQ844HUs7Ga5MIg=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			NCokbUhf77D/zQwBLqdXx/daGmIInl+zWSIBfUUJYRM=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			BH5p6QorOEEQ6JbYyFXp9Anb2HLrASDVZJBHb4Ucge8=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			hLCUt5kBipj6rlAHdcqiEsXaaVdtN79Dj3oaAqZUeiI=
			</data>
		</dict>
		<key>Resources/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
