"/Users/<USER>/Desktop/ToDo_List/macos/Runner/MainFlutterWindow.swift":
  swiftmodule: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_list_app.swiftmodule"
  const-values: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/Runner-master.swiftconstvalues"
  objc-header: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_list_app-Swift.h"
  dependencies: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/Runner-master.d"
  diagnostics: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/Runner-master.dia"
  swiftdoc: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_list_app.swiftdoc"
  swiftsourceinfo: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_list_app.swiftsourceinfo"
  abi-baseline-json: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/x86_64/todo_list_app.abi.json"
