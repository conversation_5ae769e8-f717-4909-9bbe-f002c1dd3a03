"/Users/<USER>/Desktop/ToDo_List/macos/Runner/MainFlutterWindow.swift":
  objc-header: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app-Swift.h"
  swiftsourceinfo: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app.swiftsourceinfo"
  diagnostics: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/Runner-master.dia"
  dependencies: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/Runner-master.d"
  abi-baseline-json: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app.abi.json"
  swiftmodule: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app.swiftmodule"
  swiftdoc: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app.swiftdoc"
  const-values: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/Runner-master.swiftconstvalues"
