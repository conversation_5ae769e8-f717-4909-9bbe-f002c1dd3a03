"/Users/<USER>/Desktop/ToDo_List/macos/Runner/MainFlutterWindow.swift":
  swiftmodule: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app.swiftmodule"
  const-values: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/Runner-master.swiftconstvalues"
  swiftsourceinfo: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app.swiftsourceinfo"
  diagnostics: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/Runner-master.dia"
  dependencies: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/Runner-master.d"
  swiftdoc: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app.swiftdoc"
  abi-baseline-json: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app.abi.json"
  objc-header: "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Runner.build/Release/Runner.build/Objects-normal/arm64/todo_list_app-Swift.h"
