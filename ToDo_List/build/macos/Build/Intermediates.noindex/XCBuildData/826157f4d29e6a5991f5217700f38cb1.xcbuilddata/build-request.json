{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "18c1723432283e0cc55f10a6dcfd9e02a26d6e580d3222b86d62fb80ec380c14"}], "containerPath": "/Users/<USER>/Desktop/ToDo_List/macos/Runner.xcworkspace", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "macosx", "sdk": "macosx15.4", "sdkVariant": "macos", "supportedArchitectures": ["arm64e", "arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products", "derivedDataPath": "/Users/<USER>/Desktop/ToDo_List/build/macos", "indexDataStoreFolderPath": "/Users/<USER>/Desktop/ToDo_List/build/macos/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Desktop/ToDo_List/build/macos/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Release", "overrides": {"commandLine": {"table": {"COMPILER_INDEX_STORE_ENABLE": "NO", "OBJROOT": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex", "SYMROOT": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products"}}, "synthesized": {"table": {"ACTION": "build", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}