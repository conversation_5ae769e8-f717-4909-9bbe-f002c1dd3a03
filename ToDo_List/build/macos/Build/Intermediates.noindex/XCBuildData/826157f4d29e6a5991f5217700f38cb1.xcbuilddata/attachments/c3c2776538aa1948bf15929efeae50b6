-target x86_64-apple-macos10.14 '-std=gnu11' -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Desktop/ToDo_List/build/macos/ModuleCache.noindex' '-fmodule-name=sqflite_darwin' -fpascal-strings -Os -fno-common '-DPOD_CONFIGURATION_RELEASE=1' '-DCOCOAPODS=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -fasm-blocks -g -iquote /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/sqflite_darwin.build/sqflite_darwin-generated-files.hmap -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/sqflite_darwin.build/sqflite_darwin-own-target-headers.hmap -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/sqflite_darwin.build/sqflite_darwin-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-8699adb1dd336b26511df848a716bd42-VFS/all-product-headers.yaml -iquote /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/sqflite_darwin.build/sqflite_darwin-project-headers.hmap -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/sqflite_darwin/include -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/sqflite_darwin.build/DerivedSources-normal/x86_64 -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/sqflite_darwin.build/DerivedSources/x86_64 -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/sqflite_darwin.build/DerivedSources -F/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/sqflite_darwin -F/opt/homebrew/Caskroom/flutter/3.29.0/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64