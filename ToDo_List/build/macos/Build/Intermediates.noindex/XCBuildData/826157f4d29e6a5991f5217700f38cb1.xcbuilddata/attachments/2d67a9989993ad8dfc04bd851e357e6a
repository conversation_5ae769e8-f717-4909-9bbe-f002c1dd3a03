-target x86_64-apple-macos10.14 '-std=gnu11' -fobjc-arc -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Desktop/ToDo_List/build/macos/ModuleCache.noindex' '-fmodule-name=flutter_local_notifications' -fpascal-strings -Os -fno-common '-DPOD_CONFIGURATION_RELEASE=1' '-DCOCOAPODS=1' '-DNS_BLOCK_ASSERTIONS=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -fasm-blocks -g -iquote /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/flutter_local_notifications-generated-files.hmap -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/flutter_local_notifications-own-target-headers.hmap -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/flutter_local_notifications-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-8699adb1dd336b26511df848a716bd42-VFS/all-product-headers.yaml -iquote /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/flutter_local_notifications-project-headers.hmap -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications/include -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/DerivedSources-normal/x86_64 -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/DerivedSources/x86_64 -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/DerivedSources -F/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications -F/opt/homebrew/Caskroom/flutter/3.29.0/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64