{
  "version": 0,
  "use-external-names": false,
  "case-sensitive": false,
  "roots": [{
    "type": "directory",
    "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications/flutter_local_notifications.framework/Modules",
    "contents": [{
      "type": "file",
      "name": "module.modulemap",
      "external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/unextended-module.modulemap",
    }]
    },
    {
    "type": "directory",
    "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications/flutter_local_notifications.framework/Headers",
    "contents": [{
      "type": "file",
      "name": "flutter_local_notifications-Swift.h",
      "external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/unextended-interface-header.h",
    }]
  }]
}
