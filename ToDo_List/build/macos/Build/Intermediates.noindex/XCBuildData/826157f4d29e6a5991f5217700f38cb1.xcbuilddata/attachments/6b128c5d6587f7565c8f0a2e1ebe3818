{"": {"const-values": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/Objects-normal/x86_64/flutter_local_notifications-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/Objects-normal/x86_64/flutter_local_notifications-master.d", "diagnostics": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/Objects-normal/x86_64/flutter_local_notifications-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/Objects-normal/x86_64/flutter_local_notifications-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/Objects-normal/x86_64/flutter_local_notifications-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/Objects-normal/x86_64/flutter_local_notifications-master.swiftdeps"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/macos/Classes/FlutterLocalNotificationsPlugin.swift": {"index-unit-output-path": "/Pods.build/Release/flutter_local_notifications.build/Objects-normal/x86_64/FlutterLocalNotificationsPlugin.o", "llvm-bc": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/Objects-normal/x86_64/FlutterLocalNotificationsPlugin.bc", "object": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/Objects-normal/x86_64/FlutterLocalNotificationsPlugin.o"}}