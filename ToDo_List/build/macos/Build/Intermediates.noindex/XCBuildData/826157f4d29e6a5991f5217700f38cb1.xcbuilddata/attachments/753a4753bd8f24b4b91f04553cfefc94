-target arm64-apple-macos10.14 '-std=gnu11' -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Desktop/ToDo_List/build/macos/ModuleCache.noindex' '-fmodule-name=Pods_Runner' -fpascal-strings -Os -fno-common '-DPOD_CONFIGURATION_RELEASE=1' '-DCOCOAPODS=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -g -iquote /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/Pods_Runner-generated-files.hmap -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/Pods_Runner-own-target-headers.hmap -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/Pods_Runner-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-8699adb1dd336b26511df848a716bd42-VFS/all-product-headers.yaml -iquote /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/Pods_Runner-project-headers.hmap -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/include -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications/flutter_local_notifications.framework/Headers -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/path_provider_foundation/path_provider_foundation.framework/Headers -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/shared_preferences_foundation/shared_preferences_foundation.framework/Headers -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/sqflite_darwin/sqflite_darwin.framework/Headers -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/DerivedSources-normal/arm64 -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/DerivedSources/arm64 -I/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/DerivedSources -F/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release -F/opt/homebrew/Caskroom/flutter/3.29.0/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64 -F/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications -F/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/path_provider_foundation -F/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/shared_preferences_foundation -F/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/sqflite_darwin