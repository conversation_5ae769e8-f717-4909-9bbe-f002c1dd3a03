{"case-sensitive": "false", "roots": [{"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/macos/Pods/Target Support Files/Pods-Runner/Pods-Runner-umbrella.h", "name": "Pods-Runner-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/Pods_Runner.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/Pods-Runner.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/Pods_Runner.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/macos/Classes/Converters.h", "name": "Converters.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/ToDo_List/macos/Pods/Target Support Files/flutter_local_notifications/flutter_local_notifications-umbrella.h", "name": "flutter_local_notifications-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications/flutter_local_notifications.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/flutter_local_notifications.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications/flutter_local_notifications.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications/flutter_local_notifications.framework/Headers/flutter_local_notifications-Swift.h", "name": "flutter_local_notifications-Swift.h", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/flutter_local_notifications/flutter_local_notifications.framework/Versions/A/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/macos/Pods/Target Support Files/path_provider_foundation/path_provider_foundation-umbrella.h", "name": "path_provider_foundation-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/path_provider_foundation/path_provider_foundation.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/path_provider_foundation/path_provider_foundation.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/path_provider_foundation/path_provider_foundation.framework/Headers/path_provider_foundation-Swift.h", "name": "path_provider_foundation-Swift.h", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/path_provider_foundation/path_provider_foundation.framework/Versions/A/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/macos/Pods/Target Support Files/shared_preferences_foundation/shared_preferences_foundation-umbrella.h", "name": "shared_preferences_foundation-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/shared_preferences_foundation/shared_preferences_foundation.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/shared_preferences_foundation.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/shared_preferences_foundation/shared_preferences_foundation.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/shared_preferences_foundation/shared_preferences_foundation.framework/Headers/shared_preferences_foundation-Swift.h", "name": "shared_preferences_foundation-Swift.h", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/shared_preferences_foundation/shared_preferences_foundation.framework/Versions/A/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "name": "SqfliteImportPublic.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "name": "SqflitePluginPublic.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/ToDo_List/macos/Pods/Target Support Files/sqflite_darwin/sqflite_darwin-umbrella.h", "name": "sqflite_darwin-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/sqflite_darwin/sqflite_darwin.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/sqflite_darwin.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/sqflite_darwin/sqflite_darwin.framework/Modules", "type": "directory"}], "version": 0}