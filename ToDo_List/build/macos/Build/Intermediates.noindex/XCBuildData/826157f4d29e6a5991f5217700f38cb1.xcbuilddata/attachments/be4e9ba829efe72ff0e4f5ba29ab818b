{"": {"const-values": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/path_provider_foundation-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/path_provider_foundation-master.d", "diagnostics": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/path_provider_foundation-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/path_provider_foundation-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/path_provider_foundation-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/path_provider_foundation-master.swiftdeps"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift": {"index-unit-output-path": "/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/PathProviderPlugin.o", "llvm-bc": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/PathProviderPlugin.bc", "object": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/PathProviderPlugin.o"}, "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift": {"index-unit-output-path": "/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/messages.g.o", "llvm-bc": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/messages.g.bc", "object": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/Objects-normal/arm64/messages.g.o"}}