{
  "version": 0,
  "use-external-names": false,
  "case-sensitive": false,
  "roots": [{
    "type": "directory",
    "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/path_provider_foundation/path_provider_foundation.framework/Modules",
    "contents": [{
      "type": "file",
      "name": "module.modulemap",
      "external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/unextended-module.modulemap",
    }]
    },
    {
    "type": "directory",
    "name": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/path_provider_foundation/path_provider_foundation.framework/Headers",
    "contents": [{
      "type": "file",
      "name": "path_provider_foundation-Swift.h",
      "external-contents": "/Users/<USER>/Desktop/ToDo_List/build/macos/Build/Intermediates.noindex/Pods.build/Release/path_provider_foundation.build/unextended-interface-header.h",
    }]
  }]
}
