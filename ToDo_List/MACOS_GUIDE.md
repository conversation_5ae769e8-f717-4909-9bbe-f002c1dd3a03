# TaskFlow - macOS运行指南

## 🍎 在macOS上运行TaskFlow

TaskFlow已经成功适配macOS平台，您可以通过以下方式在Mac上体验这款精美的任务管理应用。

### 🚀 快速启动

#### 方法一：直接运行构建好的应用
```bash
# 进入项目目录
cd ToDo_List

# 直接打开macOS应用
open build/macos/Build/Products/Release/todo_list_app.app
```

#### 方法二：使用Flutter命令运行
```bash
# 进入项目目录
cd ToDo_List

# 构建macOS版本
flutter build macos

# 运行应用
flutter run -d macos
```

#### 方法三：开发模式运行
```bash
# 进入项目目录
cd ToDo_List

# 安装依赖
flutter pub get

# 直接运行（开发模式，支持热重载）
flutter run -d macos
```

### 📱 应用特性

#### 1. **原生macOS体验**
- 完全适配macOS界面规范
- 支持macOS窗口管理
- 原生性能，流畅运行
- 支持macOS快捷键

#### 2. **跨平台一致性**
- 与iOS/Android版本功能完全一致
- 统一的UI设计语言
- 相同的用户体验
- 数据格式兼容

#### 3. **macOS专属优化**
- 适配macOS窗口大小
- 支持Retina显示屏
- 优化鼠标和触控板操作
- 集成macOS通知系统

### 🎨 界面展示

#### **主界面特色**
- **今日任务**: 清晰的进度展示和任务管理
- **所有任务**: 强大的搜索和筛选功能
- **统计分析**: 可视化的数据图表
- **个人设置**: 完整的配置选项

#### **macOS适配亮点**
- **窗口化界面**: 适合桌面使用的布局
- **鼠标交互**: 优化的点击和悬停效果
- **键盘支持**: 支持常用快捷键操作
- **多任务**: 可与其他macOS应用并行使用

### 🛠️ 技术特性

#### **Flutter for macOS**
- 使用Flutter 3.32.0最新版本
- 原生macOS渲染引擎
- 高性能UI渲染
- 完整的macOS API支持

#### **构建配置**
- Xcode项目自动生成
- 支持Debug和Release模式
- 代码签名和公证支持
- App Store分发准备

### 📋 系统要求

#### **最低要求**
- macOS 10.14 (Mojave) 或更高版本
- 至少4GB RAM
- 100MB可用存储空间
- 支持Metal的显卡

#### **推荐配置**
- macOS 12.0 (Monterey) 或更高版本
- 8GB或更多RAM
- SSD存储
- Retina显示屏

### 🔧 开发环境设置

#### **必需工具**
```bash
# 检查Flutter环境
flutter doctor

# 确保macOS支持已启用
flutter config --enable-macos-desktop
```

#### **Xcode配置**
- Xcode 12.0或更高版本
- macOS SDK
- 开发者账户（用于代码签名）

### 🎯 使用技巧

#### **键盘快捷键**
- `Cmd + N`: 新建任务
- `Cmd + F`: 搜索任务
- `Cmd + ,`: 打开设置
- `Cmd + W`: 关闭窗口

#### **鼠标操作**
- **单击**: 选择任务
- **双击**: 编辑任务
- **右键**: 显示上下文菜单
- **拖拽**: 调整任务顺序

#### **窗口管理**
- 支持全屏模式
- 可调整窗口大小
- 支持多桌面切换
- 最小化到Dock

### 🚀 性能优化

#### **启动优化**
- 快速启动时间
- 内存使用优化
- CPU占用最小化
- 电池续航友好

#### **运行优化**
- 流畅的动画效果
- 响应式界面更新
- 高效的数据处理
- 智能缓存机制

### 📦 分发选项

#### **开发测试**
```bash
# 构建Debug版本
flutter build macos --debug

# 构建Release版本
flutter build macos --release
```

#### **App Store准备**
```bash
# 构建用于App Store的版本
flutter build macos --release

# 应用位置
build/macos/Build/Products/Release/todo_list_app.app
```

### 🔍 故障排除

#### **常见问题**

1. **应用无法启动**
   ```bash
   # 清理构建缓存
   flutter clean
   flutter pub get
   flutter build macos
   ```

2. **权限问题**
   ```bash
   # 检查应用权限
   xattr -d com.apple.quarantine build/macos/Build/Products/Release/todo_list_app.app
   ```

3. **依赖问题**
   ```bash
   # 重新安装依赖
   cd macos
   pod install
   cd ..
   ```

### 📈 未来计划

#### **macOS专属功能**
- [ ] Touch Bar支持
- [ ] Spotlight搜索集成
- [ ] 菜单栏快速访问
- [ ] 系统通知中心集成
- [ ] iCloud同步支持

#### **性能增强**
- [ ] 更快的启动速度
- [ ] 更低的内存占用
- [ ] 更好的电池优化
- [ ] 更流畅的动画

---

## 🎉 享受TaskFlow on macOS

TaskFlow现在完美运行在您的Mac上！这款精美的任务管理应用将帮助您在macOS环境下更高效地管理任务和提升生产力。

**立即体验**: `open build/macos/Build/Products/Release/todo_list_app.app`

---

**TaskFlow** - 让任务管理在Mac上也变得简单高效 ✨
