 /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/AssetManifest.json /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/AssetManifest.bin /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/FontManifest.json /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/NOTICES.Z /Users/<USER>/Desktop/ToDo_List/build/macos/Build/Products/Release/App.framework/Versions/A/Resources/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/Desktop/ToDo_List/pubspec.yaml /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-brands-400.ttf /Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-regular-400.ttf /Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-solid-900.ttf /opt/homebrew/Caskroom/flutter/3.29.0/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /opt/homebrew/Caskroom/flutter/3.29.0/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/65cac72afcaab24ea211be85895d6e7a/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-3.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE /opt/homebrew/Caskroom/flutter/3.29.0/flutter/bin/cache/pkg/sky_engine/LICENSE /opt/homebrew/Caskroom/flutter/3.29.0/flutter/packages/flutter/LICENSE /Users/<USER>/Desktop/ToDo_List/DOES_NOT_EXIST_RERUN_FOR_WILDCARD317860933