{"inputs": ["/opt/homebrew/Caskroom/flutter/3.29.0/flutter/bin/cache/engine.stamp"], "outputs": ["/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/flutter.js", "/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/canvaskit/skwasm.js", "/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/canvaskit/skwasm.js.symbols", "/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/canvaskit/canvaskit.js.symbols", "/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/canvaskit/skwasm.wasm", "/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/canvaskit/chromium/canvaskit.js.symbols", "/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/canvaskit/chromium/canvaskit.js", "/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/canvaskit/chromium/canvaskit.wasm", "/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/canvaskit/canvaskit.js", "/Users/<USER>/Desktop/ToDo_List/.dart_tool/flutter_build/ec01b87e4977166f43d2e2d816abda45/canvaskit/canvaskit.wasm"]}