{"inputs": ["/Users/<USER>/Desktop/ToDo_List/build/web/flutter_bootstrap.js", "/Users/<USER>/Desktop/ToDo_List/build/web/version.json", "/Users/<USER>/Desktop/ToDo_List/build/web/index.html", "/Users/<USER>/Desktop/ToDo_List/build/web/main.dart.js", "/Users/<USER>/Desktop/ToDo_List/build/web/flutter.js", "/Users/<USER>/Desktop/ToDo_List/build/web/favicon.png", "/Users/<USER>/Desktop/ToDo_List/build/web/manifest.json", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/AssetManifest.json", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/NOTICES", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/FontManifest.json", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/AssetManifest.bin.json", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/AssetManifest.bin", "/Users/<USER>/Desktop/ToDo_List/build/web/assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/ToDo_List/build/web/canvaskit/skwasm.js", "/Users/<USER>/Desktop/ToDo_List/build/web/canvaskit/skwasm.js.symbols", "/Users/<USER>/Desktop/ToDo_List/build/web/canvaskit/canvaskit.js.symbols", "/Users/<USER>/Desktop/ToDo_List/build/web/canvaskit/skwasm.wasm", "/Users/<USER>/Desktop/ToDo_List/build/web/canvaskit/chromium/canvaskit.js.symbols", "/Users/<USER>/Desktop/ToDo_List/build/web/canvaskit/chromium/canvaskit.js", "/Users/<USER>/Desktop/ToDo_List/build/web/canvaskit/chromium/canvaskit.wasm", "/Users/<USER>/Desktop/ToDo_List/build/web/canvaskit/canvaskit.js", "/Users/<USER>/Desktop/ToDo_List/build/web/canvaskit/canvaskit.wasm"], "outputs": ["/Users/<USER>/Desktop/ToDo_List/build/web/flutter_service_worker.js"]}