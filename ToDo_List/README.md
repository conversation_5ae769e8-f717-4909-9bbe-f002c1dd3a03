# TaskFlow - Flutter任务清单管理App

一款专为上班族和学生党设计的现代化任务管理应用，采用Flutter开发，具有精美的iOS风格界面设计。

## 🎯 目标用户

- **上班族**: 管理工作任务、会议安排、项目进度
- **学生党**: 管理作业、考试、课程安排、学习计划

## 💡 解决的痛点

1. **执行力不足** → 任务优先级管理、进度追踪、完成激励
2. **容易遗忘** → 智能提醒、日程视图、重要任务置顶

## ✨ 核心功能

### 📱 主要界面
- **今日任务**: 专注当天任务，显示进度和成就
- **所有任务**: 完整任务列表，支持搜索和筛选
- **统计分析**: 可视化数据展示，生产力洞察
- **个人设置**: 用户配置、成就系统、偏好设置

### 🔧 核心特性
- ✅ 任务创建与分类管理
- 🎯 三级优先级系统（高/中/低）
- 📂 五大分类（工作/个人/学习/健康/其他）
- ⏰ 智能提醒和截止日期
- 📊 详细统计和进度追踪
- 🏆 成就系统和连续完成记录
- 🎨 现代化iOS风格界面

## 🎨 设计特色

### UI/UX 设计
- **iPhone 15 Pro尺寸适配**: 真实手机界面体验
- **iOS状态栏**: 模拟真实iOS状态栏显示
- **圆角设计**: 现代化圆角界面元素
- **渐变色彩**: 精美的蓝色渐变主题
- **阴影效果**: 层次分明的卡片设计
- **FontAwesome图标**: 丰富的矢量图标库

### 交互体验
- **直观操作**: 点击完成、滑动删除
- **快速添加**: 浮动按钮快速创建任务
- **智能筛选**: 多维度任务筛选
- **实时搜索**: 即时搜索任务内容
- **进度可视化**: 直观的进度条和图表

## 🏗️ 技术架构

### 开发框架
- **Flutter**: 跨平台移动应用开发
- **Dart**: 现代化编程语言
- **Material Design**: Google设计规范

### 核心依赖
```yaml
dependencies:
  flutter: sdk
  font_awesome_flutter: ^10.6.0  # 图标库
  google_fonts: ^6.1.0           # 字体库
  intl: ^0.18.1                  # 国际化
  shared_preferences: ^2.2.2     # 本地存储
  flutter_local_notifications: ^16.3.0  # 本地通知
  sqflite: ^2.3.0               # SQLite数据库
```

### 项目结构
```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   └── task.dart            # 任务模型
├── screens/                  # 界面页面
│   ├── home_screen.dart     # 主界面
│   ├── today_screen.dart    # 今日任务
│   ├── tasks_screen.dart    # 所有任务
│   ├── stats_screen.dart    # 统计分析
│   ├── profile_screen.dart  # 个人设置
│   └── add_task_screen.dart # 添加任务
├── widgets/                  # 自定义组件
│   ├── ios_status_bar.dart  # iOS状态栏
│   └── task_card.dart       # 任务卡片
└── utils/                    # 工具类
    └── app_colors.dart      # 颜色主题
```

## 🚀 快速开始

### 环境要求
- Flutter SDK >= 3.0.0
- Dart SDK >= 3.0.0
- iOS 11.0+ / Android API 21+

### 安装步骤
1. 克隆项目
```bash
git clone <repository-url>
cd ToDo_List
```

2. 安装依赖
```bash
flutter pub get
```

3. 运行应用
```bash
flutter run
```

## 📱 界面预览

### 主要功能界面
- **今日任务**: 显示当天任务和完成进度
- **任务管理**: 完整的任务CRUD操作
- **数据统计**: 可视化的生产力分析
- **个人中心**: 设置和成就系统

### 设计亮点
- 模拟iPhone 15 Pro的真实界面
- iOS风格的状态栏和导航
- 现代化的卡片式设计
- 直观的颜色编码系统
- 流畅的动画和交互

## 🎯 产品特色

### 用户体验
- **简洁直观**: 清晰的信息层级和操作流程
- **高效管理**: 快速创建、编辑和完成任务
- **智能提醒**: 基于时间和优先级的提醒系统
- **数据洞察**: 帮助用户了解自己的工作习惯

### 技术优势
- **跨平台**: 一套代码支持iOS和Android
- **高性能**: Flutter原生渲染性能
- **可扩展**: 模块化架构便于功能扩展
- **现代化**: 采用最新的Flutter技术栈

## 📈 未来规划

- [ ] 云端同步功能
- [ ] 团队协作功能
- [ ] 更多统计图表
- [ ] 主题自定义
- [ ] 语音输入
- [ ] AI智能建议

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**TaskFlow** - 让任务管理变得简单高效 ✨
