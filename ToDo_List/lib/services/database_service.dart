import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/task.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'tasks.db');
    return await openDatabase(
      path,
      version: 2,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE tasks(
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        createdAt INTEGER NOT NULL,
        dueDate INTEGER,
        priority INTEGER NOT NULL,
        category INTEGER NOT NULL,
        isCompleted INTEGER NOT NULL,
        tags TEXT,
        parentId TEXT,
        estimatedMinutes INTEGER,
        actualMinutes INTEGER,
        reminderTime INTEGER,
        isRecurring INTEGER DEFAULT 0,
        recurringType TEXT,
        completedAt INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE categories(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        color TEXT NOT NULL,
        icon TEXT NOT NULL,
        isDefault INTEGER DEFAULT 0
      )
    ''');

    await db.execute('''
      CREATE TABLE settings(
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    ''');

    // 插入默认分类
    await _insertDefaultCategories(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await db.execute('ALTER TABLE tasks ADD COLUMN parentId TEXT');
      await db.execute('ALTER TABLE tasks ADD COLUMN estimatedMinutes INTEGER');
      await db.execute('ALTER TABLE tasks ADD COLUMN actualMinutes INTEGER');
      await db.execute('ALTER TABLE tasks ADD COLUMN reminderTime INTEGER');
      await db.execute('ALTER TABLE tasks ADD COLUMN isRecurring INTEGER DEFAULT 0');
      await db.execute('ALTER TABLE tasks ADD COLUMN recurringType TEXT');
      await db.execute('ALTER TABLE tasks ADD COLUMN completedAt INTEGER');
    }
  }

  Future<void> _insertDefaultCategories(Database db) async {
    final defaultCategories = [
      {'name': '工作', 'color': '#2196F3', 'icon': 'briefcase', 'isDefault': 1},
      {'name': '个人', 'color': '#4CAF50', 'icon': 'user', 'isDefault': 1},
      {'name': '学习', 'color': '#FF9800', 'icon': 'graduation-cap', 'isDefault': 1},
      {'name': '健康', 'color': '#E91E63', 'icon': 'heart-pulse', 'isDefault': 1},
      {'name': '其他', 'color': '#9C27B0', 'icon': 'ellipsis', 'isDefault': 1},
    ];

    for (var category in defaultCategories) {
      await db.insert('categories', category);
    }
  }

  // 任务CRUD操作
  Future<int> insertTask(Task task) async {
    final db = await database;
    return await db.insert('tasks', task.toJson());
  }

  Future<List<Task>> getAllTasks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      orderBy: 'createdAt DESC',
    );
    return List.generate(maps.length, (i) => Task.fromJson(maps[i]));
  }

  Future<List<Task>> getTasksByDate(DateTime date) async {
    final db = await database;
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'dueDate >= ? AND dueDate < ?',
      whereArgs: [startOfDay.millisecondsSinceEpoch, endOfDay.millisecondsSinceEpoch],
      orderBy: 'priority DESC, createdAt DESC',
    );
    return List.generate(maps.length, (i) => Task.fromJson(maps[i]));
  }

  Future<List<Task>> getSubTasks(String parentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'parentId = ?',
      whereArgs: [parentId],
      orderBy: 'createdAt ASC',
    );
    return List.generate(maps.length, (i) => Task.fromJson(maps[i]));
  }

  Future<int> updateTask(Task task) async {
    final db = await database;
    return await db.update(
      'tasks',
      task.toJson(),
      where: 'id = ?',
      whereArgs: [task.id],
    );
  }

  Future<int> deleteTask(String id) async {
    final db = await database;
    // 同时删除子任务
    await db.delete('tasks', where: 'parentId = ?', whereArgs: [id]);
    return await db.delete('tasks', where: 'id = ?', whereArgs: [id]);
  }

  Future<int> toggleTaskComplete(String id) async {
    final db = await database;
    final task = await getTask(id);
    if (task != null) {
      final updatedTask = task.copyWith(
        isCompleted: !task.isCompleted,
        completedAt: !task.isCompleted ? DateTime.now() : null,
      );
      return await updateTask(updatedTask);
    }
    return 0;
  }

  Future<Task?> getTask(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Task.fromJson(maps.first);
    }
    return null;
  }

  // 统计数据
  Future<Map<String, int>> getTaskStats() async {
    final db = await database;
    
    final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM tasks');
    final completedResult = await db.rawQuery('SELECT COUNT(*) as count FROM tasks WHERE isCompleted = 1');
    final todayResult = await db.rawQuery('''
      SELECT COUNT(*) as count FROM tasks 
      WHERE dueDate >= ? AND dueDate < ?
    ''', [
      DateTime.now().millisecondsSinceEpoch,
      DateTime.now().add(const Duration(days: 1)).millisecondsSinceEpoch,
    ]);
    
    return {
      'total': totalResult.first['count'] as int,
      'completed': completedResult.first['count'] as int,
      'today': todayResult.first['count'] as int,
    };
  }

  Future<List<Map<String, dynamic>>> getWeeklyStats() async {
    final db = await database;
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    
    List<Map<String, dynamic>> weeklyData = [];
    
    for (int i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      final dayStart = DateTime(day.year, day.month, day.day);
      final dayEnd = dayStart.add(const Duration(days: 1));
      
      final result = await db.rawQuery('''
        SELECT COUNT(*) as completed FROM tasks 
        WHERE isCompleted = 1 AND completedAt >= ? AND completedAt < ?
      ''', [dayStart.millisecondsSinceEpoch, dayEnd.millisecondsSinceEpoch]);
      
      weeklyData.add({
        'day': day,
        'completed': result.first['completed'] as int,
      });
    }
    
    return weeklyData;
  }

  // 设置相关
  Future<void> setSetting(String key, String value) async {
    final db = await database;
    await db.insert(
      'settings',
      {'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
    );
    if (maps.isNotEmpty) {
      return maps.first['value'] as String;
    }
    return null;
  }

  // 清理数据
  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('tasks');
    await db.delete('settings');
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
