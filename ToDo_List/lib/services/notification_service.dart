import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  bool _initialized = false;

  Future<void> initialize() async {
    if (_initialized) return;

    // 初始化时区
    tz.initializeTimeZones();

    // Android 初始化设置
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

    // macOS 初始化设置
    const DarwinInitializationSettings macOSSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
      macOS: macOSSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // 请求权限
    await _requestPermissions();

    _initialized = true;
  }

  Future<void> _requestPermissions() async {
    if (defaultTargetPlatform == TargetPlatform.macOS) {
      await _notifications
          .resolvePlatformSpecificImplementation<MacOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          _notifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

      await androidImplementation?.requestNotificationsPermission();
    }
  }

  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    // 这里可以处理通知点击事件，比如导航到特定页面
  }

  Future<void> scheduleNotification(
    int id,
    String title,
    String body,
    DateTime scheduledTime, {
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    try {
      final tz.TZDateTime tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.local);

      // 如果时间已过，不设置通知
      if (tzScheduledTime.isBefore(tz.TZDateTime.now(tz.local))) {
        debugPrint('Scheduled time is in the past, skipping notification');
        return;
      }

      const NotificationDetails notificationDetails = NotificationDetails(
        android: AndroidNotificationDetails(
          'task_reminders',
          '任务提醒',
          channelDescription: '任务截止时间和提醒通知',
          importance: Importance.high,
          priority: Priority.high,
          showWhen: true,
        ),
        macOS: DarwinNotificationDetails(
          categoryIdentifier: 'task_reminder',
          threadIdentifier: 'task_thread',
        ),
      );

      await _notifications.zonedSchedule(
        id,
        title,
        body,
        tzScheduledTime,
        notificationDetails,
        payload: payload,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time,
      );

      debugPrint('Notification scheduled for $scheduledTime');
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
    }
  }

  Future<void> scheduleRepeatingNotification(
    int id,
    String title,
    String body,
    DateTime scheduledTime,
    RepeatInterval repeatInterval, {
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    try {
      final tz.TZDateTime tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.local);

      const NotificationDetails notificationDetails = NotificationDetails(
        android: AndroidNotificationDetails(
          'recurring_tasks',
          '重复任务',
          channelDescription: '重复任务提醒通知',
          importance: Importance.high,
          priority: Priority.high,
        ),
        macOS: DarwinNotificationDetails(
          categoryIdentifier: 'recurring_task',
          threadIdentifier: 'recurring_thread',
        ),
      );

      await _notifications.periodicallyShow(
        id,
        title,
        body,
        repeatInterval,
        notificationDetails,
        payload: payload,
      );

      debugPrint('Repeating notification scheduled');
    } catch (e) {
      debugPrint('Error scheduling repeating notification: $e');
    }
  }

  Future<void> showInstantNotification(
    int id,
    String title,
    String body, {
    String? payload,
  }) async {
    if (!_initialized) await initialize();

    const NotificationDetails notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        'instant_notifications',
        '即时通知',
        channelDescription: '即时任务通知',
        importance: Importance.high,
        priority: Priority.high,
      ),
      macOS: DarwinNotificationDetails(
        categoryIdentifier: 'instant',
        threadIdentifier: 'instant_thread',
      ),
    );

    await _notifications.show(
      id,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
    debugPrint('Notification $id cancelled');
  }

  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
    debugPrint('All notifications cancelled');
  }

  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  Future<void> scheduleTaskReminder(
    String taskId,
    String taskTitle,
    DateTime reminderTime,
  ) async {
    await scheduleNotification(
      taskId.hashCode,
      '任务提醒',
      '别忘了完成：$taskTitle',
      reminderTime,
      payload: taskId,
    );
  }

  Future<void> scheduleTaskDeadline(
    String taskId,
    String taskTitle,
    DateTime deadline,
  ) async {
    // 在截止时间前1小时提醒
    final reminderTime = deadline.subtract(const Duration(hours: 1));
    
    if (reminderTime.isAfter(DateTime.now())) {
      await scheduleNotification(
        '${taskId}_deadline'.hashCode,
        '任务即将到期',
        '任务"$taskTitle"将在1小时后到期',
        reminderTime,
        payload: taskId,
      );
    }
  }

  Future<void> notifyTaskCompleted(String taskTitle) async {
    await showInstantNotification(
      DateTime.now().millisecondsSinceEpoch,
      '任务完成',
      '恭喜！您已完成任务：$taskTitle',
    );
  }

  Future<void> scheduleDailyReminder(int hour, int minute) async {
    final now = DateTime.now();
    var scheduledTime = DateTime(now.year, now.month, now.day, hour, minute);
    
    // 如果今天的时间已过，安排到明天
    if (scheduledTime.isBefore(now)) {
      scheduledTime = scheduledTime.add(const Duration(days: 1));
    }

    await scheduleRepeatingNotification(
      'daily_reminder'.hashCode,
      '每日任务提醒',
      '查看今天的任务安排',
      scheduledTime,
      RepeatInterval.daily,
    );
  }

  Future<bool> areNotificationsEnabled() async {
    if (defaultTargetPlatform == TargetPlatform.macOS) {
      final macOSImplementation = _notifications
          .resolvePlatformSpecificImplementation<MacOSFlutterLocalNotificationsPlugin>();
      return await macOSImplementation?.checkPermissions() ?? false;
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      final androidImplementation = _notifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
      return await androidImplementation?.areNotificationsEnabled() ?? false;
    }
    return false;
  }
}
