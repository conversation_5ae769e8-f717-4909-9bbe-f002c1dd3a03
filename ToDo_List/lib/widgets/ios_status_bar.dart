import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';

class IOSStatusBar extends StatelessWidget {
  const IOSStatusBar({super.key});

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final timeFormat = DateFormat('HH:mm');
    
    return Container(
      height: 44,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(
            color: AppColors.textTertiary,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left side - Time
          Text(
            timeFormat.format(now),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          // Center - Dynamic Island (iPhone 15 Pro style)
          Container(
            width: 120,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.textPrimary,
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          
          // Right side - Battery and signal
          Row(
            children: [
              // Signal bars
              Row(
                children: List.generate(4, (index) {
                  return Container(
                    width: 3,
                    height: 4 + (index * 2).toDouble(),
                    margin: const EdgeInsets.only(right: 2),
                    decoration: BoxDecoration(
                      color: AppColors.textPrimary,
                      borderRadius: BorderRadius.circular(1),
                    ),
                  );
                }),
              ),
              const SizedBox(width: 6),
              
              // WiFi icon
              const Icon(
                Icons.wifi,
                size: 16,
                color: AppColors.textPrimary,
              ),
              const SizedBox(width: 6),
              
              // Battery
              Container(
                width: 24,
                height: 12,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.textPrimary,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Stack(
                  children: [
                    // Battery fill
                    Positioned(
                      left: 1,
                      top: 1,
                      bottom: 1,
                      child: Container(
                        width: 18, // 80% battery
                        decoration: BoxDecoration(
                          color: AppColors.success,
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),
                    ),
                    // Battery tip
                    Positioned(
                      right: -2,
                      top: 3,
                      bottom: 3,
                      child: Container(
                        width: 2,
                        decoration: BoxDecoration(
                          color: AppColors.textPrimary,
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
