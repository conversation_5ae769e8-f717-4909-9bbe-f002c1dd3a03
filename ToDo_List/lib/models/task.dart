import 'package:intl/intl.dart';

enum TaskPriority { normal, high }
enum TaskCategory { work, personal, other }

class Task {
  final String id;
  final String title;
  final String description;
  final DateTime createdAt;
  final DateTime? dueDate;
  final TaskPriority priority;
  final TaskCategory category;
  final bool isCompleted;
  final List<String> tags;
  final String? parentId; // 用于子任务
  final int? estimatedMinutes; // 预估时间
  final int? actualMinutes; // 实际用时
  final DateTime? reminderTime; // 提醒时间
  final bool isRecurring; // 是否重复
  final String? recurringType; // 重复类型
  final DateTime? completedAt; // 完成时间

  Task({
    required this.id,
    required this.title,
    this.description = '',
    required this.createdAt,
    this.dueDate,
    this.priority = TaskPriority.normal,
    this.category = TaskCategory.personal,
    this.isCompleted = false,
    this.tags = const [],
    this.parentId,
    this.estimatedMinutes,
    this.actualMinutes,
    this.reminderTime,
    this.isRecurring = false,
    this.recurringType,
    this.completedAt,
  });

  Task copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? createdAt,
    DateTime? dueDate,
    TaskPriority? priority,
    TaskCategory? category,
    bool? isCompleted,
    List<String>? tags,
    String? parentId,
    int? estimatedMinutes,
    int? actualMinutes,
    DateTime? reminderTime,
    bool? isRecurring,
    String? recurringType,
    DateTime? completedAt,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      dueDate: dueDate ?? this.dueDate,
      priority: priority ?? this.priority,
      category: category ?? this.category,
      isCompleted: isCompleted ?? this.isCompleted,
      tags: tags ?? this.tags,
      parentId: parentId ?? this.parentId,
      estimatedMinutes: estimatedMinutes ?? this.estimatedMinutes,
      actualMinutes: actualMinutes ?? this.actualMinutes,
      reminderTime: reminderTime ?? this.reminderTime,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringType: recurringType ?? this.recurringType,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'priority': priority.index,
      'category': category.index,
      'isCompleted': isCompleted ? 1 : 0,
      'tags': tags.join(','),
      'parentId': parentId,
      'estimatedMinutes': estimatedMinutes,
      'actualMinutes': actualMinutes,
      'reminderTime': reminderTime?.millisecondsSinceEpoch,
      'isRecurring': isRecurring ? 1 : 0,
      'recurringType': recurringType,
      'completedAt': completedAt?.millisecondsSinceEpoch,
    };
  }

  factory Task.fromJson(Map<String, dynamic> json) {
    return Task(
      id: json['id'],
      title: json['title'],
      description: json['description'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      dueDate: json['dueDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['dueDate'])
          : null,
      priority: TaskPriority.values[json['priority'] ?? 0],
      category: TaskCategory.values[json['category'] ?? 1],
      isCompleted: json['isCompleted'] == 1,
      tags: json['tags'] != null && json['tags'].isNotEmpty
          ? json['tags'].split(',')
          : [],
      parentId: json['parentId'],
      estimatedMinutes: json['estimatedMinutes'],
      actualMinutes: json['actualMinutes'],
      reminderTime: json['reminderTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['reminderTime'])
          : null,
      isRecurring: json['isRecurring'] == 1,
      recurringType: json['recurringType'],
      completedAt: json['completedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['completedAt'])
          : null,
    );
  }

  String get formattedDueDate {
    if (dueDate == null) return '';
    return DateFormat('MM月dd日').format(dueDate!);
  }

  String get priorityText {
    switch (priority) {
      case TaskPriority.high:
        return '重要';
      case TaskPriority.normal:
        return '普通';
    }
  }

  String get categoryText {
    switch (category) {
      case TaskCategory.work:
        return '工作';
      case TaskCategory.personal:
        return '个人';
      case TaskCategory.other:
        return '其他';
    }
  }

  bool get isOverdue {
    if (dueDate == null || isCompleted) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  bool get isDueToday {
    if (dueDate == null) return false;
    final now = DateTime.now();
    return dueDate!.year == now.year &&
           dueDate!.month == now.month &&
           dueDate!.day == now.day;
  }

  bool get isSubTask => parentId != null;

  String get formattedEstimatedTime {
    if (estimatedMinutes == null) return '';
    final hours = estimatedMinutes! ~/ 60;
    final minutes = estimatedMinutes! % 60;
    if (hours > 0) {
      return '${hours}小时${minutes > 0 ? '${minutes}分钟' : ''}';
    }
    return '${minutes}分钟';
  }

  String get formattedActualTime {
    if (actualMinutes == null) return '';
    final hours = actualMinutes! ~/ 60;
    final minutes = actualMinutes! % 60;
    if (hours > 0) {
      return '${hours}小时${minutes > 0 ? '${minutes}分钟' : ''}';
    }
    return '${minutes}分钟';
  }

  String get formattedReminderTime {
    if (reminderTime == null) return '';
    return DateFormat('MM月dd日 HH:mm').format(reminderTime!);
  }

  bool get hasReminder => reminderTime != null;

  bool get isUpcoming {
    if (dueDate == null || isCompleted) return false;
    final now = DateTime.now();
    final tomorrow = now.add(const Duration(days: 1));
    return dueDate!.isAfter(now) && dueDate!.isBefore(tomorrow);
  }

  int get daysUntilDue {
    if (dueDate == null) return 0;
    final now = DateTime.now();
    return dueDate!.difference(now).inDays;
  }
}
