import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../utils/app_colors.dart';
import '../providers/task_provider.dart';
import '../services/notification_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final NotificationService _notificationService = NotificationService();
  bool _notificationsEnabled = false;
  bool _dailyReminder = false;
  TimeOfDay _reminderTime = const TimeOfDay(hour: 9, minute: 0);

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final enabled = await _notificationService.areNotificationsEnabled();
    setState(() {
      _notificationsEnabled = enabled;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          '设置',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        backgroundColor: AppColors.background,
        elevation: 0,
        centerTitle: false,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 通知设置
          _buildSectionCard(
            title: '通知设置',
            icon: FontAwesomeIcons.bell,
            children: [
              _buildSwitchTile(
                title: '启用通知',
                subtitle: '接收任务提醒和截止日期通知',
                value: _notificationsEnabled,
                onChanged: (value) async {
                  if (value) {
                    await _notificationService.initialize();
                  }
                  setState(() {
                    _notificationsEnabled = value;
                  });
                },
              ),
              if (_notificationsEnabled) ...[
                const Divider(height: 1),
                _buildSwitchTile(
                  title: '每日提醒',
                  subtitle: '每天定时提醒查看任务',
                  value: _dailyReminder,
                  onChanged: (value) async {
                    setState(() {
                      _dailyReminder = value;
                    });
                    if (value) {
                      await _notificationService.scheduleDailyReminder(
                        _reminderTime.hour,
                        _reminderTime.minute,
                      );
                    }
                  },
                ),
                if (_dailyReminder) ...[
                  const Divider(height: 1),
                  _buildTimeTile(
                    title: '提醒时间',
                    time: _reminderTime,
                    onChanged: (time) async {
                      setState(() {
                        _reminderTime = time;
                      });
                      await _notificationService.scheduleDailyReminder(
                        time.hour,
                        time.minute,
                      );
                    },
                  ),
                ],
              ],
            ],
          ),

          const SizedBox(height: 16),

          // 数据管理
          _buildSectionCard(
            title: '数据管理',
            icon: FontAwesomeIcons.database,
            children: [
              _buildActionTile(
                title: '导出数据',
                subtitle: '将任务数据导出为文件',
                icon: FontAwesomeIcons.download,
                onTap: () {
                  _showExportDialog();
                },
              ),
              const Divider(height: 1),
              _buildActionTile(
                title: '清空所有数据',
                subtitle: '删除所有任务和设置（不可恢复）',
                icon: FontAwesomeIcons.trash,
                isDestructive: true,
                onTap: () {
                  _showClearDataDialog();
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 应用信息
          _buildSectionCard(
            title: '关于应用',
            icon: FontAwesomeIcons.circleInfo,
            children: [
              _buildInfoTile(
                title: '版本',
                value: '升级版 v2.0',
              ),
              const Divider(height: 1),
              _buildInfoTile(
                title: '开发者',
                value: 'Augment Agent',
              ),
              const Divider(height: 1),
              _buildActionTile(
                title: '使用帮助',
                subtitle: '查看应用使用说明',
                icon: FontAwesomeIcons.circleQuestion,
                onTap: () {
                  _showHelpDialog();
                },
              ),
            ],
          ),

          const SizedBox(height: 32),

          // 统计信息
          Consumer<TaskProvider>(
            builder: (context, taskProvider, child) {
              return _buildStatsCard(taskProvider);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: FaIcon(
                    icon,
                    size: 16,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          fontSize: 14,
          color: AppColors.textSecondary,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildTimeTile({
    required String title,
    required TimeOfDay time,
    required ValueChanged<TimeOfDay> onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
        ),
      ),
      trailing: InkWell(
        onTap: () async {
          final newTime = await showTimePicker(
            context: context,
            initialTime: time,
          );
          if (newTime != null) {
            onChanged(newTime);
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            time.format(context),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: FaIcon(
        icon,
        size: 16,
        color: isDestructive ? AppColors.error : AppColors.textSecondary,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: isDestructive ? AppColors.error : AppColors.textPrimary,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          fontSize: 14,
          color: AppColors.textSecondary,
        ),
      ),
      trailing: const FaIcon(
        FontAwesomeIcons.chevronRight,
        size: 12,
        color: AppColors.textTertiary,
      ),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile({
    required String title,
    required String value,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
        ),
      ),
      trailing: Text(
        value,
        style: const TextStyle(
          fontSize: 14,
          color: AppColors.textSecondary,
        ),
      ),
    );
  }

  Widget _buildStatsCard(TaskProvider taskProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '使用统计',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  '总任务',
                  '${taskProvider.totalTasks}',
                  FontAwesomeIcons.listCheck,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '已完成',
                  '${taskProvider.completedTasks}',
                  FontAwesomeIcons.circleCheck,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '完成率',
                  '${(taskProvider.completionRate * 100).round()}%',
                  FontAwesomeIcons.chartLine,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FaIcon(
            icon,
            size: 16,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导出数据'),
        content: const Text('此功能将在后续版本中实现。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空所有数据'),
        content: const Text('此操作将删除所有任务和设置，且无法恢复。确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await context.read<TaskProvider>().clearAllData();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('所有数据已清空')),
                );
              }
            },
            child: const Text('确定', style: TextStyle(color: AppColors.error)),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('使用帮助'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('• 在"今日"页面查看今天的任务'),
              SizedBox(height: 8),
              Text('• 在"任务"页面管理所有任务'),
              SizedBox(height: 8),
              Text('• 在"统计"页面查看完成情况'),
              SizedBox(height: 8),
              Text('• 向左滑动任务可快速删除'),
              SizedBox(height: 8),
              Text('• 点击任务可查看详情'),
              SizedBox(height: 8),
              Text('• 设置提醒时间接收通知'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }
}
