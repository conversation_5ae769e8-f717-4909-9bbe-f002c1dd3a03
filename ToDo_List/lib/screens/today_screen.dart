import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../models/task.dart';
import '../widgets/task_card.dart';
import '../utils/app_colors.dart';

class TodayScreen extends StatefulWidget {
  const TodayScreen({super.key});

  @override
  State<TodayScreen> createState() => _TodayScreenState();
}

class _TodayScreenState extends State<TodayScreen> {
  List<Task> _todayTasks = [];
  List<Task> _overdueTasks = [];

  @override
  void initState() {
    super.initState();
    _loadTodayTasks();
  }

  void _loadTodayTasks() {
    // Sample data - in real app, this would come from database
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    _todayTasks = [
      Task(
        id: '1',
        title: '完成项目汇报',
        description: '准备季度总结会议的演示文稿',
        createdAt: today,
        dueDate: today.add(const Duration(hours: 14)),
        priority: TaskPriority.high,
        category: TaskCategory.work,
      ),
      Task(
        id: '2',
        title: '学习Flutter文档',
        description: '研究新的组件和最佳实践',
        createdAt: today,
        dueDate: today.add(const Duration(hours: 18)),
        priority: TaskPriority.medium,
        category: TaskCategory.study,
      ),
      Task(
        id: '3',
        title: '购买生活用品',
        description: '买晚餐需要的食材',
        createdAt: today,
        priority: TaskPriority.low,
        category: TaskCategory.personal,
      ),
    ];

    _overdueTasks = [
      Task(
        id: '4',
        title: '提交报销单',
        description: '上传发票并填写报销表格',
        createdAt: yesterday,
        dueDate: yesterday,
        priority: TaskPriority.high,
        category: TaskCategory.work,
      ),
    ];
  }

  void _toggleTaskComplete(Task task) {
    setState(() {
      final updatedTask = task.copyWith(isCompleted: !task.isCompleted);

      if (_todayTasks.contains(task)) {
        final index = _todayTasks.indexOf(task);
        _todayTasks[index] = updatedTask;
      } else if (_overdueTasks.contains(task)) {
        final index = _overdueTasks.indexOf(task);
        _overdueTasks[index] = updatedTask;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final dateFormat = DateFormat('EEEE, MMMM d');
    final completedToday = _todayTasks.where((task) => task.isCompleted).length;
    final totalToday = _todayTasks.length;

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: CustomScrollView(
        slivers: [
          // Header
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    dateFormat.format(now),
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '今日任务',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Progress card
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: AppColors.primaryGradient,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                '今日进度',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '已完成 $completedToday / $totalToday 项任务',
                                style: const TextStyle(
                                  fontSize: 24,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 12),
                              LinearProgressIndicator(
                                value: totalToday > 0 ? completedToday / totalToday : 0,
                                backgroundColor: Colors.white.withOpacity(0.3),
                                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 20),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: const FaIcon(
                            FontAwesomeIcons.trophy,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Overdue tasks section
          if (_overdueTasks.isNotEmpty) ...[
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: Row(
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.triangleExclamation,
                      color: AppColors.error,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '逾期任务 (${_overdueTasks.length})',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.error,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final task = _overdueTasks[index];
                  return TaskCard(
                    task: task,
                    onToggleComplete: () => _toggleTaskComplete(task),
                  );
                },
                childCount: _overdueTasks.length,
              ),
            ),
          ],

          // Today's tasks section
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Row(
                children: [
                  const FaIcon(
                    FontAwesomeIcons.calendarDay,
                    color: AppColors.primary,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '今日任务 (${_todayTasks.length})',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ),

          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final task = _todayTasks[index];
                return TaskCard(
                  task: task,
                  onToggleComplete: () => _toggleTaskComplete(task),
                );
              },
              childCount: _todayTasks.length,
            ),
          ),

          // Bottom padding for FAB
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
    );
  }
}
