import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../widgets/ios_status_bar.dart';
import '../utils/app_colors.dart';
import 'today_screen.dart';
import 'tasks_screen.dart';
import 'stats_screen.dart';
import 'profile_screen.dart';
import 'add_task_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const TodayScreen(),
    const TasksScreen(),
    const StatsScreen(),
    const ProfileScreen(),
  ];

  final List<BottomNavigationBarItem> _navItems = [
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.calendarDay, size: 20),
      activeIcon: FaIcon(FontAwesomeIcons.calendarDay, size: 22),
      label: 'Today',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.listCheck, size: 20),
      activeIcon: FaIcon(FontAwesomeIcons.listCheck, size: 22),
      label: 'Tasks',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.chartLine, size: 20),
      activeIcon: FaIcon(FontAwesomeIcons.chartLine, size: 22),
      label: 'Stats',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.user, size: 20),
      activeIcon: FaIcon(FontAwesomeIcons.user, size: 22),
      label: 'Profile',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          top: false,
          child: Column(
            children: [
              const IOSStatusBar(),
              Expanded(
                child: _screens[_currentIndex],
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            items: _navItems,
            type: BottomNavigationBarType.fixed,
            backgroundColor: AppColors.surface,
            selectedItemColor: AppColors.primary,
            unselectedItemColor: AppColors.textSecondary,
            selectedFontSize: 12,
            unselectedFontSize: 12,
            elevation: 0,
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const AddTaskScreen(),
            ),
          );
        },
        backgroundColor: AppColors.primary,
        child: const Icon(
          FontAwesomeIcons.plus,
          color: Colors.white,
          size: 20,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }
}
