import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../utils/app_colors.dart';
import '../models/task.dart';
import '../widgets/task_card.dart';
import '../providers/task_provider.dart';
import 'add_task_screen.dart';
import 'today_screen.dart';
import 'tasks_screen.dart';
import 'stats_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<Widget> _screens = [
    const TodayScreen(),
    const TasksScreen(),
    const StatsScreen(),
    const SettingsScreen(),
  ];

  final List<BottomNavigationBarItem> _navItems = [
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.calendarDay, size: 20),
      activeIcon: FaIcon(FontAwesomeIcons.calendarDay, size: 22),
      label: '今日',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.listCheck, size: 20),
      activeIcon: FaIcon(FontAwesomeIcons.listCheck, size: 22),
      label: '任务',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.chartLine, size: 20),
      activeIcon: FaIcon(FontAwesomeIcons.chartLine, size: 22),
      label: '统计',
    ),
    const BottomNavigationBarItem(
      icon: FaIcon(FontAwesomeIcons.gear, size: 20),
      activeIcon: FaIcon(FontAwesomeIcons.gear, size: 22),
      label: '设置',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // 初始化TaskProvider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TaskProvider>().initialize();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: _screens,
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: _onTabTapped,
            items: _navItems,
            type: BottomNavigationBarType.fixed,
            backgroundColor: AppColors.surface,
            selectedItemColor: AppColors.primary,
            unselectedItemColor: AppColors.textSecondary,
            selectedFontSize: 12,
            unselectedFontSize: 12,
            elevation: 0,
          ),
        ),
      ),
      floatingActionButton: _currentIndex < 2 ? FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.of(context).push<Task>(
            MaterialPageRoute(
              builder: (context) => const AddTaskScreen(),
            ),
          );
          if (result != null) {
            context.read<TaskProvider>().addTask(result);
          }
        },
        backgroundColor: AppColors.primary,
        child: const Icon(
          FontAwesomeIcons.plus,
          color: Colors.white,
          size: 20,
        ),
      ) : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }
}
