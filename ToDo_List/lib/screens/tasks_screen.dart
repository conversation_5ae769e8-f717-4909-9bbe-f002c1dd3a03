import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/task.dart';
import '../widgets/task_card.dart';
import '../utils/app_colors.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen> {
  List<Task> _allTasks = [];
  List<Task> _filteredTasks = [];
  TaskCategory? _selectedCategory;
  TaskPriority? _selectedPriority;
  bool _showCompleted = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAllTasks();
  }

  void _loadAllTasks() {
    final now = DateTime.now();

    _allTasks = [
      Task(
        id: '1',
        title: '完成项目汇报',
        description: '准备季度总结会议的演示文稿',
        createdAt: now.subtract(const Duration(hours: 2)),
        dueDate: now.add(const Duration(hours: 14)),
        priority: TaskPriority.high,
        category: TaskCategory.work,
      ),
      Task(
        id: '2',
        title: '学习Flutter文档',
        description: '研究新的组件和最佳实践',
        createdAt: now.subtract(const Duration(hours: 1)),
        dueDate: now.add(const Duration(hours: 18)),
        priority: TaskPriority.medium,
        category: TaskCategory.study,
      ),
      Task(
        id: '3',
        title: '购买生活用品',
        description: '买晚餐需要的食材',
        createdAt: now.subtract(const Duration(minutes: 30)),
        priority: TaskPriority.low,
        category: TaskCategory.personal,
      ),
      Task(
        id: '4',
        title: '提交报销单',
        description: '上传发票并填写报销表格',
        createdAt: now.subtract(const Duration(days: 1)),
        dueDate: now.subtract(const Duration(hours: 2)),
        priority: TaskPriority.high,
        category: TaskCategory.work,
      ),
      Task(
        id: '5',
        title: '晨练锻炼',
        description: '30分钟有氧运动和力量训练',
        createdAt: now.subtract(const Duration(hours: 6)),
        priority: TaskPriority.medium,
        category: TaskCategory.health,
        isCompleted: true,
      ),
      Task(
        id: '6',
        title: '阅读研究论文',
        description: '了解最新的AI技术发展',
        createdAt: now.subtract(const Duration(days: 2)),
        dueDate: now.add(const Duration(days: 3)),
        priority: TaskPriority.low,
        category: TaskCategory.study,
      ),
    ];

    _applyFilters();
  }

  void _applyFilters() {
    setState(() {
      _filteredTasks = _allTasks.where((task) {
        // Search filter
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          if (!task.title.toLowerCase().contains(query) &&
              !task.description.toLowerCase().contains(query)) {
            return false;
          }
        }

        // Category filter
        if (_selectedCategory != null && task.category != _selectedCategory) {
          return false;
        }

        // Priority filter
        if (_selectedPriority != null && task.priority != _selectedPriority) {
          return false;
        }

        // Completed filter
        if (!_showCompleted && task.isCompleted) {
          return false;
        }

        return true;
      }).toList();

      // Sort by priority and due date
      _filteredTasks.sort((a, b) {
        if (a.isCompleted != b.isCompleted) {
          return a.isCompleted ? 1 : -1;
        }

        if (a.priority != b.priority) {
          return b.priority.index.compareTo(a.priority.index);
        }

        if (a.dueDate != null && b.dueDate != null) {
          return a.dueDate!.compareTo(b.dueDate!);
        }

        return a.createdAt.compareTo(b.createdAt);
      });
    });
  }

  void _toggleTaskComplete(Task task) {
    setState(() {
      final index = _allTasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        _allTasks[index] = task.copyWith(isCompleted: !task.isCompleted);
        _applyFilters();
      }
    });
  }

  void _deleteTask(Task task) {
    setState(() {
      _allTasks.removeWhere((t) => t.id == task.id);
      _applyFilters();
    });
  }

  String _getCategoryName(TaskCategory category) {
    switch (category) {
      case TaskCategory.work:
        return '工作';
      case TaskCategory.personal:
        return '个人';
      case TaskCategory.study:
        return '学习';
      case TaskCategory.health:
        return '健康';
      case TaskCategory.other:
        return '其他';
    }
  }

  String _getPriorityName(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return '高';
      case TaskPriority.medium:
        return '中';
      case TaskPriority.low:
        return '低';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: CustomScrollView(
        slivers: [
          // Header with search and filters
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '所有任务',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Search bar
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      onChanged: (value) {
                        _searchQuery = value;
                        _applyFilters();
                      },
                      decoration: const InputDecoration(
                        hintText: '搜索任务...',
                        prefixIcon: Icon(
                          FontAwesomeIcons.magnifyingGlass,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Filter chips
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        // Show completed toggle
                        FilterChip(
                          label: Text('显示已完成'),
                          selected: _showCompleted,
                          onSelected: (selected) {
                            _showCompleted = selected;
                            _applyFilters();
                          },
                          backgroundColor: AppColors.surface,
                          selectedColor: AppColors.primary.withOpacity(0.2),
                          checkmarkColor: AppColors.primary,
                        ),

                        const SizedBox(width: 8),

                        // Category filters
                        ...TaskCategory.values.map((category) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text(_getCategoryName(category)),
                              selected: _selectedCategory == category,
                              onSelected: (selected) {
                                _selectedCategory = selected ? category : null;
                                _applyFilters();
                              },
                              backgroundColor: AppColors.surface,
                              selectedColor: AppColors.primary.withOpacity(0.2),
                              checkmarkColor: AppColors.primary,
                            ),
                          );
                        }).toList(),

                        // Priority filters
                        ...TaskPriority.values.map((priority) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text('${_getPriorityName(priority)}优先级'),
                              selected: _selectedPriority == priority,
                              onSelected: (selected) {
                                _selectedPriority = selected ? priority : null;
                                _applyFilters();
                              },
                              backgroundColor: AppColors.surface,
                              selectedColor: AppColors.primary.withOpacity(0.2),
                              checkmarkColor: AppColors.primary,
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Task count
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Text(
                '共 ${_filteredTasks.length} 项任务',
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

          // Tasks list
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final task = _filteredTasks[index];
                return TaskCard(
                  task: task,
                  onToggleComplete: () => _toggleTaskComplete(task),
                  onDelete: () => _deleteTask(task),
                );
              },
              childCount: _filteredTasks.length,
            ),
          ),

          // Empty state
          if (_filteredTasks.isEmpty)
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.all(40),
                child: Column(
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.listCheck,
                      size: 64,
                      color: AppColors.textTertiary,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '暂无任务',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '尝试调整筛选条件或添加新任务',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textTertiary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Bottom padding for FAB
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
    );
  }
}
