import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/task.dart';
import '../widgets/task_card.dart';
import '../utils/app_colors.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen> {
  List<Task> _allTasks = [];
  List<Task> _filteredTasks = [];
  TaskCategory? _selectedCategory;
  TaskPriority? _selectedPriority;
  bool _showCompleted = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAllTasks();
  }

  void _loadAllTasks() {
    final now = DateTime.now();
    
    _allTasks = [
      Task(
        id: '1',
        title: 'Complete project presentation',
        description: 'Prepare slides for quarterly review meeting',
        createdAt: now.subtract(const Duration(hours: 2)),
        dueDate: now.add(const Duration(hours: 14)),
        priority: TaskPriority.high,
        category: TaskCategory.work,
      ),
      Task(
        id: '2',
        title: 'Review Flutter documentation',
        description: 'Study new widgets and best practices',
        createdAt: now.subtract(const Duration(hours: 1)),
        dueDate: now.add(const Duration(hours: 18)),
        priority: TaskPriority.medium,
        category: TaskCategory.study,
      ),
      Task(
        id: '3',
        title: 'Grocery shopping',
        description: 'Buy ingredients for dinner',
        createdAt: now.subtract(const Duration(minutes: 30)),
        priority: TaskPriority.low,
        category: TaskCategory.personal,
      ),
      Task(
        id: '4',
        title: 'Submit expense report',
        description: 'Upload receipts and fill out forms',
        createdAt: now.subtract(const Duration(days: 1)),
        dueDate: now.subtract(const Duration(hours: 2)),
        priority: TaskPriority.high,
        category: TaskCategory.work,
      ),
      Task(
        id: '5',
        title: 'Morning workout',
        description: '30 minutes cardio and strength training',
        createdAt: now.subtract(const Duration(hours: 6)),
        priority: TaskPriority.medium,
        category: TaskCategory.health,
        isCompleted: true,
      ),
      Task(
        id: '6',
        title: 'Read research paper',
        description: 'Review latest AI developments',
        createdAt: now.subtract(const Duration(days: 2)),
        dueDate: now.add(const Duration(days: 3)),
        priority: TaskPriority.low,
        category: TaskCategory.study,
      ),
    ];
    
    _applyFilters();
  }

  void _applyFilters() {
    setState(() {
      _filteredTasks = _allTasks.where((task) {
        // Search filter
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          if (!task.title.toLowerCase().contains(query) &&
              !task.description.toLowerCase().contains(query)) {
            return false;
          }
        }
        
        // Category filter
        if (_selectedCategory != null && task.category != _selectedCategory) {
          return false;
        }
        
        // Priority filter
        if (_selectedPriority != null && task.priority != _selectedPriority) {
          return false;
        }
        
        // Completed filter
        if (!_showCompleted && task.isCompleted) {
          return false;
        }
        
        return true;
      }).toList();
      
      // Sort by priority and due date
      _filteredTasks.sort((a, b) {
        if (a.isCompleted != b.isCompleted) {
          return a.isCompleted ? 1 : -1;
        }
        
        if (a.priority != b.priority) {
          return b.priority.index.compareTo(a.priority.index);
        }
        
        if (a.dueDate != null && b.dueDate != null) {
          return a.dueDate!.compareTo(b.dueDate!);
        }
        
        return a.createdAt.compareTo(b.createdAt);
      });
    });
  }

  void _toggleTaskComplete(Task task) {
    setState(() {
      final index = _allTasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        _allTasks[index] = task.copyWith(isCompleted: !task.isCompleted);
        _applyFilters();
      }
    });
  }

  void _deleteTask(Task task) {
    setState(() {
      _allTasks.removeWhere((t) => t.id == task.id);
      _applyFilters();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: CustomScrollView(
        slivers: [
          // Header with search and filters
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'All Tasks',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 20),
                  
                  // Search bar
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      onChanged: (value) {
                        _searchQuery = value;
                        _applyFilters();
                      },
                      decoration: const InputDecoration(
                        hintText: 'Search tasks...',
                        prefixIcon: Icon(
                          FontAwesomeIcons.magnifyingGlass,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Filter chips
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        // Show completed toggle
                        FilterChip(
                          label: Text('Show Completed'),
                          selected: _showCompleted,
                          onSelected: (selected) {
                            _showCompleted = selected;
                            _applyFilters();
                          },
                          backgroundColor: AppColors.surface,
                          selectedColor: AppColors.primary.withOpacity(0.2),
                          checkmarkColor: AppColors.primary,
                        ),
                        
                        const SizedBox(width: 8),
                        
                        // Category filters
                        ...TaskCategory.values.map((category) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text(category.name.toUpperCase()),
                              selected: _selectedCategory == category,
                              onSelected: (selected) {
                                _selectedCategory = selected ? category : null;
                                _applyFilters();
                              },
                              backgroundColor: AppColors.surface,
                              selectedColor: AppColors.primary.withOpacity(0.2),
                              checkmarkColor: AppColors.primary,
                            ),
                          );
                        }).toList(),
                        
                        // Priority filters
                        ...TaskPriority.values.map((priority) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text('${priority.name.toUpperCase()} PRIORITY'),
                              selected: _selectedPriority == priority,
                              onSelected: (selected) {
                                _selectedPriority = selected ? priority : null;
                                _applyFilters();
                              },
                              backgroundColor: AppColors.surface,
                              selectedColor: AppColors.primary.withOpacity(0.2),
                              checkmarkColor: AppColors.primary,
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Task count
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Text(
                '${_filteredTasks.length} tasks',
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          
          // Tasks list
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final task = _filteredTasks[index];
                return TaskCard(
                  task: task,
                  onToggleComplete: () => _toggleTaskComplete(task),
                  onDelete: () => _deleteTask(task),
                );
              },
              childCount: _filteredTasks.length,
            ),
          ),
          
          // Empty state
          if (_filteredTasks.isEmpty)
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.all(40),
                child: Column(
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.listCheck,
                      size: 64,
                      color: AppColors.textTertiary,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'No tasks found',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Try adjusting your filters or add a new task',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textTertiary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          
          // Bottom padding for FAB
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
    );
  }
}
