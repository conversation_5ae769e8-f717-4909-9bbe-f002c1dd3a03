import 'package:flutter/foundation.dart';
import '../models/task.dart';
import '../services/database_service.dart';
import '../services/notification_service.dart';

class TaskProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final NotificationService _notificationService = NotificationService();

  List<Task> _tasks = [];
  List<Task> _filteredTasks = [];
  String _searchQuery = '';
  TaskFilter _currentFilter = TaskFilter.all;
  TaskSort _currentSort = TaskSort.priority;
  bool _isLoading = false;

  // Getters
  List<Task> get tasks => _filteredTasks;
  List<Task> get allTasks => _tasks;
  String get searchQuery => _searchQuery;
  TaskFilter get currentFilter => _currentFilter;
  TaskSort get currentSort => _currentSort;
  bool get isLoading => _isLoading;

  // 统计数据
  int get totalTasks => _tasks.length;
  int get completedTasks => _tasks.where((task) => task.isCompleted).length;
  int get pendingTasks => _tasks.where((task) => !task.isCompleted).length;
  int get todayTasks => _tasks.where((task) => task.isDueToday).length;
  int get overdueTasks => _tasks.where((task) => task.isOverdue).length;
  int get upcomingTasks => _tasks.where((task) => task.isUpcoming).length;

  double get completionRate {
    if (_tasks.isEmpty) return 0.0;
    return completedTasks / totalTasks;
  }

  // 初始化
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      await _notificationService.initialize();
      await loadTasks();
    } catch (e) {
      debugPrint('Error initializing TaskProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 加载任务
  Future<void> loadTasks() async {
    try {
      _tasks = await _databaseService.getAllTasks();
      _applyFiltersAndSort();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading tasks: $e');
    }
  }

  // 添加任务
  Future<void> addTask(Task task) async {
    try {
      await _databaseService.insertTask(task);
      
      // 设置提醒
      if (task.hasReminder) {
        await _notificationService.scheduleNotification(
          task.id.hashCode,
          '任务提醒',
          task.title,
          task.reminderTime!,
        );
      }
      
      await loadTasks();
    } catch (e) {
      debugPrint('Error adding task: $e');
    }
  }

  // 更新任务
  Future<void> updateTask(Task task) async {
    try {
      await _databaseService.updateTask(task);
      
      // 更新提醒
      await _notificationService.cancelNotification(task.id.hashCode);
      if (task.hasReminder && !task.isCompleted) {
        await _notificationService.scheduleNotification(
          task.id.hashCode,
          '任务提醒',
          task.title,
          task.reminderTime!,
        );
      }
      
      await loadTasks();
    } catch (e) {
      debugPrint('Error updating task: $e');
    }
  }

  // 删除任务
  Future<void> deleteTask(String taskId) async {
    try {
      await _databaseService.deleteTask(taskId);
      await _notificationService.cancelNotification(taskId.hashCode);
      await loadTasks();
    } catch (e) {
      debugPrint('Error deleting task: $e');
    }
  }

  // 切换任务完成状态
  Future<void> toggleTaskComplete(String taskId) async {
    try {
      await _databaseService.toggleTaskComplete(taskId);
      
      // 如果任务完成，取消提醒
      final task = _tasks.firstWhere((t) => t.id == taskId);
      if (task.isCompleted) {
        await _notificationService.cancelNotification(taskId.hashCode);
      }
      
      await loadTasks();
    } catch (e) {
      debugPrint('Error toggling task: $e');
    }
  }

  // 获取子任务
  Future<List<Task>> getSubTasks(String parentId) async {
    try {
      return await _databaseService.getSubTasks(parentId);
    } catch (e) {
      debugPrint('Error getting subtasks: $e');
      return [];
    }
  }

  // 搜索任务
  void searchTasks(String query) {
    _searchQuery = query;
    _applyFiltersAndSort();
    notifyListeners();
  }

  // 设置筛选器
  void setFilter(TaskFilter filter) {
    _currentFilter = filter;
    _applyFiltersAndSort();
    notifyListeners();
  }

  // 设置排序
  void setSort(TaskSort sort) {
    _currentSort = sort;
    _applyFiltersAndSort();
    notifyListeners();
  }

  // 应用筛选和排序
  void _applyFiltersAndSort() {
    List<Task> filtered = List.from(_tasks);

    // 应用搜索
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((task) {
        return task.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               task.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               task.tags.any((tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()));
      }).toList();
    }

    // 应用筛选器
    switch (_currentFilter) {
      case TaskFilter.pending:
        filtered = filtered.where((task) => !task.isCompleted).toList();
        break;
      case TaskFilter.completed:
        filtered = filtered.where((task) => task.isCompleted).toList();
        break;
      case TaskFilter.today:
        filtered = filtered.where((task) => task.isDueToday).toList();
        break;
      case TaskFilter.overdue:
        filtered = filtered.where((task) => task.isOverdue).toList();
        break;
      case TaskFilter.upcoming:
        filtered = filtered.where((task) => task.isUpcoming).toList();
        break;
      case TaskFilter.important:
        filtered = filtered.where((task) => task.priority == TaskPriority.high).toList();
        break;
      case TaskFilter.all:
        break;
    }

    // 应用排序
    switch (_currentSort) {
      case TaskSort.priority:
        filtered.sort((a, b) {
          if (a.isCompleted != b.isCompleted) {
            return a.isCompleted ? 1 : -1;
          }
          if (a.priority != b.priority) {
            return b.priority.index.compareTo(a.priority.index);
          }
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case TaskSort.dueDate:
        filtered.sort((a, b) {
          if (a.isCompleted != b.isCompleted) {
            return a.isCompleted ? 1 : -1;
          }
          if (a.dueDate == null && b.dueDate == null) return 0;
          if (a.dueDate == null) return 1;
          if (b.dueDate == null) return -1;
          return a.dueDate!.compareTo(b.dueDate!);
        });
        break;
      case TaskSort.created:
        filtered.sort((a, b) {
          if (a.isCompleted != b.isCompleted) {
            return a.isCompleted ? 1 : -1;
          }
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case TaskSort.alphabetical:
        filtered.sort((a, b) {
          if (a.isCompleted != b.isCompleted) {
            return a.isCompleted ? 1 : -1;
          }
          return a.title.compareTo(b.title);
        });
        break;
    }

    _filteredTasks = filtered;
  }

  // 获取统计数据
  Future<Map<String, int>> getStats() async {
    try {
      return await _databaseService.getTaskStats();
    } catch (e) {
      debugPrint('Error getting stats: $e');
      return {};
    }
  }

  // 获取周统计
  Future<List<Map<String, dynamic>>> getWeeklyStats() async {
    try {
      return await _databaseService.getWeeklyStats();
    } catch (e) {
      debugPrint('Error getting weekly stats: $e');
      return [];
    }
  }

  // 清理所有数据
  Future<void> clearAllData() async {
    try {
      await _databaseService.clearAllData();
      await _notificationService.cancelAllNotifications();
      await loadTasks();
    } catch (e) {
      debugPrint('Error clearing data: $e');
    }
  }
}

// 筛选器枚举
enum TaskFilter {
  all,
  pending,
  completed,
  today,
  overdue,
  upcoming,
  important,
}

// 排序枚举
enum TaskSort {
  priority,
  dueDate,
  created,
  alphabetical,
}
